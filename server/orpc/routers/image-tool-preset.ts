import { z } from "zod";
import { protectedProcedure } from "../init";
import { getUUIDString } from "@/lib/utils";
import { saveToR2 } from "@/server/r2.server";
import { OSS_URL_HOST, WEBNAME } from "@/lib/constants";
import { checkUserCredit, updateUserCredit } from "@/server/utils-credits.server";
import { genGemini2_5FromFal } from "@/server/ai/gemini.server";
import { getDB } from "@/server/db/db-client.server";
import { MediaHead, mediaHeadSchema, mediaItemSchema } from "@/server/db/schema.server";
import { MediaHeadToolType, MediaHeadType, MediaResultStatus } from "@/@types/media/media-type";
import { mixpanelTrackEvent } from "@/server/mixpanel.server";
import { EVENT_EDIT_IMAGE_WITH_TOOL } from "@/lib/track-events";
import { constructErrorFalA<PERSON>, handleApiErrorEvent } from "@/@types/error-api";
import { handleUnifiedError } from "@/@types/error";
import { removeBackground } from "@/server/ai/remove-background";

export const imageToolPresetRouter = {
	removeObject: protectedProcedure
		.input(
			z.object({
				prompt: z.string().min(1, "Prompt is required"),
				image: z.string().url("Invalid image URL"),
				tool: z.string().optional().default("image-editor"),
			}),
		)
		.handler(async ({ input, context }) => {
			try {
				// 验证用户身份
				const userId = context.sessionUser?.id!;
				// 检查用户积分
				const needCredits = 5;
				const { creditConsumes, membershipLevel } = await checkUserCredit(userId, {
					needCredits: needCredits,
				});

				console.log("removeObject params: ", input);
				console.log("creditConsumes: ", creditConsumes);

				// 追踪事件
				mixpanelTrackEvent(EVENT_EDIT_IMAGE_WITH_TOOL, userId, {
					mp_country_code: context.cfIpCountryCode,
					ip: context.cfIp,
					membershipLevel: membershipLevel,
					tool: input.tool,
				});

				// 调用 AI 服务生成图片
				const resultUrls: string[] = await genGemini2_5FromFal(input.prompt, 1, [input.image]);

				// 保存到 R2
				const imagePaths = await Promise.all(resultUrls.map((url) => saveToR2(url)));

				// 保存到数据库
				const db = getDB();
				const imageResultId = getUUIDString();
				await db.transaction(async (tx) => {
					const [media]: MediaHead[] = await tx
						.insert(mediaHeadSchema)
						.values({
							uid: imageResultId,
							userId: userId,
							status: MediaResultStatus.Completed,
							type: MediaHeadType.Image,
							tool: MediaHeadToolType.ImageTool,
							visibility: false,
							prompt: input.prompt,
							creditsSources: JSON.stringify(creditConsumes),
						})
						.returning();

					await tx.insert(mediaItemSchema).values(
						imagePaths.map((imagePath, index) => ({
							uid: getUUIDString(),
							userId: userId,
							mediaHeadUid: imageResultId,
							visibility: false,
							mediaPath: imagePath,
						})),
					);
				});

				// 更新用户积分
				await updateUserCredit(userId, creditConsumes, {
					remark: `Image generation result head uid: ${imageResultId}.`,
				});

				// 返回结果
				const resultUrl = `${OSS_URL_HOST}/${imagePaths[0]}`;
				return {
					resultUrl: resultUrl,
					imageResultId: imageResultId,
				};
			} catch (error) {
				const finalError = constructErrorFalAI(error);
				handleApiErrorEvent(finalError, `${WEBNAME} - rpc: image.removeObject`);
				throw handleUnifiedError(finalError, "rpc");
				// return handleApiErrorRPC(finalError, `${WEBNAME} - rpc: image.removeObject`);
			}
		}),

	removeBackground: protectedProcedure
		.input(
			z.object({
				imageUrl: z.string().url(),
			}),
		)
		.handler(async ({ input, context }) => {
			try {
				// 验证用户身份
				const userId = context.sessionUser?.id!;
				// 检查用户积分
				const needCredits = 2;
				const { creditConsumes, membershipLevel } = await checkUserCredit(userId, {
					needCredits: needCredits,
				});

				console.log("removeBackground params: ", input);
				console.log("creditConsumes: ", creditConsumes);

				// 追踪事件
				mixpanelTrackEvent(EVENT_EDIT_IMAGE_WITH_TOOL, userId, {
					mp_country_code: context.cfIpCountryCode,
					ip: context.cfIp,
					membershipLevel: membershipLevel,
					tool: "remove-background",
				});

				// 调用 AI 服务生成图片
				const resultUrl: string = await removeBackground(input.imageUrl);

				// 保存到 R2
				const imagePath = await saveToR2(resultUrl);

				// 保存到数据库
				const db = getDB();
				const imageResultId = getUUIDString();
				await db.transaction(async (tx) => {
					const [media]: MediaHead[] = await tx
						.insert(mediaHeadSchema)
						.values({
							uid: imageResultId,
							userId: userId,
							status: MediaResultStatus.Completed,
							type: MediaHeadType.Image,
							tool: MediaHeadToolType.ImageTool,
							visibility: false,
							creditsSources: JSON.stringify(creditConsumes),
						})
						.returning();

					await tx.insert(mediaItemSchema).values({
						uid: getUUIDString(),
						userId: userId,
						mediaHeadUid: imageResultId,
						visibility: false,
						mediaPath: imagePath,
					});
				});

				// 更新用户积分
				await updateUserCredit(userId, creditConsumes, {
					remark: `Image remove background result head uid: ${imageResultId}.`,
				});

				// 返回结果
				return {
					url: `${OSS_URL_HOST}/${imagePath}`,
				};
			} catch (error) {
				const finalError = constructErrorFalAI(error);
				handleApiErrorEvent(finalError, `${WEBNAME} - rpc: image.removeBackground`);
				throw handleUnifiedError(finalError, "rpc");
			}
		}),
};
