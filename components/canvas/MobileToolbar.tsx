import React from "react";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { Copy, Crop, Group, Download, Trash2, Layers, ChevronUp, ChevronDown, MoveUp, MoveDown } from "lucide-react";
import type { PlacedImage } from "@/@types/canvas";
import { Icons } from "../icon/icons";
import { downloadImages } from "@/lib/file/utils-canvas-download";
import { toast } from "sonner";

interface MobileToolbarProps {
	selectedIds: string[];
	images: PlacedImage[];
	handleDuplicate: () => void;
	handleRemoveBackground: () => void;
	handleCombineImages: () => void;
	handleDelete: () => void;
	setCroppingImageId: (id: string | null) => void;
	sendToFront: () => void;
	sendToBack: () => void;
	bringForward: () => void;
	sendBackward: () => void;
}

export const MobileToolbar: React.FC<MobileToolbarProps> = ({
	selectedIds,
	images,
	handleDuplicate,
	handleRemoveBackground,
	handleCombineImages,
	handleDelete,
	setCroppingImageId,
	sendToFront,
	sendToBack,
	bringForward,
	sendBackward,
}) => {
	const hasSelectedImages = selectedIds.length > 0;
	const hasSingleSelected = selectedIds.length === 1;
	const hasMultipleSelected = selectedIds.length > 1;

	// Don't show toolbar if no images are selected
	if (!hasSelectedImages) {
		return null;
	}

	const handleDownload = async () => {
		try {
			const imagesToDownload = selectedIds
				.map((id) => images.find((img) => img.id === id))
				.filter((img): img is PlacedImage => img !== undefined)
				.map((img, index) => ({
					src: img.src,
					fileName: `image-${Date.now()}-${index + 1}`,
				}));

			if (imagesToDownload.length > 0) {
				await downloadImages(imagesToDownload);
			}
		} catch (error) {
			console.error("Download failed:", error);
			toast.error("Download failed");
		}
	};

	return (
		<div
			className={cn(
				"rounded-xl border border-gray-200 bg-white/90 shadow-lg backdrop-blur-sm",
				"flex flex-col items-center gap-1 p-2 md:hidden",
				"transition-all duration-300 ease-in-out",
			)}
		>
			<Button variant="ghost" size="sm" onClick={handleDuplicate} className="h-8 w-8 p-0 text-neutral-700" title="Duplicate">
				<Copy className="h-4 w-4" />
			</Button>
			{/* Single image actions. Crop、Remove Background暂时注释掉，不开放，以后再开放 */}
			{/* {hasSingleSelected && (
				<>
					<Button variant="ghost" size="sm" onClick={() => setCroppingImageId(selectedIds[0])} className="h-8 w-8 p-0 text-neutral-700" title="Crop">
						<Crop className="h-4 w-4" />
					</Button>
					<Button variant="ghost" size="sm" onClick={handleRemoveBackground} className="h-8 w-8 p-0 text-neutral-700" title="Remove Background">
						<Icons.BackgroundTransparent className="size-3.5" />
					</Button>
				</>
			)} */}

			{/* Multiple images actions. Group暂时注释掉，不开放，以后再开放 */}
			{/* {hasMultipleSelected && (
				<Button variant="ghost" size="sm" onClick={handleCombineImages} className="h-8 w-8 p-0 text-neutral-700" title="Group">
					<Group className="h-4 w-4" />
				</Button>
			)} */}

			{/* Layers dropdown */}
			<DropdownMenu>
				<DropdownMenuTrigger asChild>
					<Button variant="ghost" size="sm" className="h-8 w-8 p-0 text-neutral-700" title="Layer Order">
						<Layers className="h-4 w-4" />
					</Button>
				</DropdownMenuTrigger>
				<DropdownMenuContent side="right" sideOffset={10} alignOffset={-4} align="start" className="bg-background w-48 space-y-1 rounded-xl border p-1">
					<DropdownMenuItem asChild>
						<Button variant="secondary" size="sm" onClick={bringForward} className="h-8 w-full cursor-pointer justify-start gap-3 px-3">
							<ChevronUp className="h-4 w-4" />
							<span className="font-normal">Move up</span>
						</Button>
					</DropdownMenuItem>
					<DropdownMenuItem asChild>
						<Button variant="secondary" size="sm" onClick={sendBackward} className="h-8 w-full cursor-pointer justify-start gap-3 px-3">
							<ChevronDown className="h-4 w-4" />
							<span className="font-normal">Move down</span>
						</Button>
					</DropdownMenuItem>
					<DropdownMenuItem asChild>
						<Button variant="secondary" size="sm" onClick={sendToFront} className="h-8 w-full cursor-pointer justify-start gap-3 px-3">
							<MoveUp className="h-4 w-4" />
							<span className="font-normal">Bring to front</span>
						</Button>
					</DropdownMenuItem>
					<DropdownMenuItem asChild>
						<Button variant="secondary" size="sm" onClick={sendToBack} className="h-8 w-full cursor-pointer justify-start gap-3 px-3">
							<MoveDown className="h-4 w-4" />
							<span className="font-normal">Send to back</span>
						</Button>
					</DropdownMenuItem>
				</DropdownMenuContent>
			</DropdownMenu>

			{/* Common actions for all selections */}
			<Button variant="ghost" size="sm" onClick={handleDownload} className="h-8 w-8 p-0 text-neutral-700" title="Download">
				<Download className="h-4 w-4" />
			</Button>
			<Button variant="ghost" size="sm" onClick={handleDelete} className="h-8 w-8 p-0 text-red-600 hover:bg-red-50 hover:text-red-700" title="Delete">
				<Trash2 className="h-4 w-4" />
			</Button>
		</div>
	);
};
